<template>
  <BasicModal title="导入项目" width="600">
    <div class="p-4">
      <div class="mb-4">
        <h4 class="mb-2">导入说明：</h4>
        <ul class="text-sm text-gray-600">
          <li>1. 请先下载导入模板，按照模板格式填写数据</li>
          <li>2. 支持的文件格式：.xlsx、.xls</li>
          <li>3. 单次最多导入1000条数据</li>
          <li>4. 如果数据已存在，可选择是否覆盖</li>
        </ul>
      </div>

      <div class="mb-4">
        <UploadDragger
          v-model:file-list="fileList"
          :before-upload="() => false"
          accept=".xlsx,.xls"
          :max-count="1"
        >
          <p class="ant-upload-drag-icon">
            <component :is="h(InBoxIcon)" />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">支持单个文件上传，仅支持 .xlsx、.xls 格式</p>
        </UploadDragger>
      </div>

      <div class="mb-4">
        <Switch v-model:checked="checked" />
        <span class="ml-2">是否覆盖已有数据</span>
      </div>

      <div class="text-center">
        <a @click="downloadTemplate" class="text-blue-500 hover:text-blue-700">
          <component :is="h(ExcelIcon)" class="mr-1" />
          下载导入模板
        </a>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue/es/upload/interface';

import { h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { ExcelIcon, InBoxIcon } from '@vben/icons';

import { Modal, Switch, Upload } from 'ant-design-vue';

import {
  downloadProjectImportTemplate,
  importProject,
} from '#/api/bpo/project';
import { commonDownloadExcel } from '#/utils/file/download';

const emit = defineEmits<{ reload: [] }>();

const UploadDragger = Upload.Dragger;

const [BasicModal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
});

const fileList = ref<UploadFile[]>([]);
const checked = ref(false);



async function handleSubmit() {
  try {
    if (fileList.value.length === 0) {
      Modal.warning({
        title: '提示',
        content: '请选择要导入的文件',
      });
      return;
    }

    const file = fileList.value[0];
    const formData = new FormData();
    formData.append('file', file.originFileObj as File);
    formData.append('updateSupport', checked.value.toString());

    await importProject(formData);
    Modal.success({
      title: '导入成功',
      content: '项目数据导入成功',
    });

    modalApi.close();
    emit('reload');
  } catch (error) {
    console.error('导入失败:', error);
    Modal.error({
      title: '导入失败',
      content: '请检查文件格式和数据是否正确',
    });
  }
}

function handleCancel() {
  fileList.value = [];
  checked.value = false;
}

async function downloadTemplate() {
  try {
    await commonDownloadExcel(downloadProjectImportTemplate, '项目导入模板.xlsx');
  } catch (error) {
    console.error('下载模板失败:', error);
  }
}
</script>
