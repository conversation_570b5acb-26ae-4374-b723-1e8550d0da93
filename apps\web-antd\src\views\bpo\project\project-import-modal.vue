<template>
  <VbenModal
    :title="'导入项目'"
    :loading="loading"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="p-4">
      <Alert
        message="导入说明"
        type="info"
        show-icon
        class="mb-4"
      >
        <template #description>
          <div>
            <p>1. 请先下载导入模板，按照模板格式填写数据</p>
            <p>2. 支持的文件格式：.xlsx、.xls</p>
            <p>3. 单次最多导入1000条数据</p>
            <p>4. 如果数据已存在，可选择是否覆盖</p>
          </div>
        </template>
      </Alert>

      <VbenForm
        ref="formRef"
        :schema="importSchema"
      />

      <div class="mt-4">
        <Button
          type="primary"
          ghost
          @click="handleDownloadTemplate"
        >
          <Icon icon="lucide:download" class="mr-2" />
          下载导入模板
        </Button>
      </div>
    </div>
  </VbenModal>
</template>

<script setup lang="ts">
import { ref, unref } from 'vue';
import { <PERSON><PERSON>, But<PERSON>, message } from 'ant-design-vue';

import type { ProjectImportParam } from '#/api/bpo/project/model';
import type { FormSchemaGetter, VbenFormInstance } from '#/components/form';

import { Icon } from '#/components/icon';
import { VbenForm } from '#/components/form';
import { VbenModal, useVbenModal } from '#/components/modal';

import {
  downloadProjectImportTemplate,
  importProject,
} from '#/api/bpo/project';

defineOptions({
  name: 'ProjectImportModal',
});

const emit = defineEmits<{
  success: [];
}>();

const [modalApi] = useVbenModal();

const formRef = ref<VbenFormInstance>();
const loading = ref(false);

// 导入表单配置
const importSchema: FormSchemaGetter = () => [
  {
    fieldName: 'file',
    label: '选择文件',
    component: 'Upload',
    componentProps: {
      accept: '.xlsx,.xls',
      maxCount: 1,
      beforeUpload: () => false, // 阻止自动上传
      listType: 'text',
    },
    rules: [
      {
        required: true,
        message: '请选择要导入的文件',
      },
    ],
  },
  {
    fieldName: 'updateSupport',
    label: '是否覆盖已有数据',
    component: 'Switch',
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
    defaultValue: false,
  },
];

/**
 * 下载导入模板
 */
async function handleDownloadTemplate() {
  try {
    loading.value = true;
    await downloadProjectImportTemplate();
    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 提交导入
 */
async function handleSubmit() {
  try {
    const formInstance = unref(formRef);
    if (!formInstance) {
      return;
    }

    // 验证表单
    const valid = await formInstance.validate();
    if (!valid) {
      return;
    }

    loading.value = true;
    const values = await formInstance.getValues();
    console.log('导入表单数据:', values);

    // 检查文件
    if (!values.file || !values.file.length) {
      message.error('请选择要导入的文件');
      return;
    }

    const file = values.file[0];
    const importData: ProjectImportParam = {
      file: file.originFileObj || file,
      updateSupport: values.updateSupport || false,
    };

    console.log('准备导入数据:', importData);

    const result = await importProject(importData);
    console.log('导入结果:', result);

    if (result.code === 200) {
      message.success(result.msg || '导入成功');
      // 关闭模态框
      modalApi.close();
      // 触发成功事件
      emit('success');
    } else {
      message.error(result.msg || '导入失败');
    }
  } catch (error) {
    console.error('导入项目失败:', error);
    message.error('导入失败，请检查文件格式和数据');
  } finally {
    loading.value = false;
  }
}

/**
 * 取消导入
 */
function handleCancel() {
  modalApi.close();
}

/**
 * 模态框打开时重置表单
 */
modalApi.onOpenChange(async (isOpen) => {
  if (isOpen) {
    await formRef.value?.resetFields();
  }
});
</script>
