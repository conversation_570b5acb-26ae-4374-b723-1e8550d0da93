import type { FormSchemaGetter } from '#/types';

import { getCustomerList } from '#/api/bpo/customer';
import { getDeptList } from '#/api/system/dept';
import { getUserList } from '#/api/system/user';

/**
 * 项目编辑表单配置
 */
export const drawerSchemaGetter: FormSchemaGetter = () => [
  {
    fieldName: 'id',
    label: 'ID',
    component: 'Input',
    dependencies: {
      show: false,
    },
  },
  {
    fieldName: 'projectName',
    label: '项目名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目名称',
    },
    rules: [
      {
        required: true,
        message: '请输入项目名称',
      },
      {
        max: 100,
        message: '项目名称长度不能超过100个字符',
      },
    ],
  },
  {
    fieldName: 'projectCode',
    label: '项目编码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目编码',
    },
    rules: [
      {
        max: 50,
        message: '项目编码长度不能超过50个字符',
      },
    ],
  },
  {
    fieldName: 'projectDescription',
    label: '项目描述',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入项目描述',
      rows: 3,
    },
    rules: [
      {
        max: 500,
        message: '项目描述长度不能超过500个字符',
      },
    ],
  },
  {
    fieldName: 'customerId',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择客户',
      api: getCustomerList,
      labelField: 'customerName',
      valueField: 'id',
    },
    rules: [
      {
        required: true,
        message: '请选择客户',
      },
    ],
  },
  {
    fieldName: 'businessType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择业务类型',
      options: [
        { label: '外包服务', value: '外包服务' },
        { label: '人力派遣', value: '人力派遣' },
        { label: '技术咨询', value: '技术咨询' },
        { label: '项目外包', value: '项目外包' },
      ],
    },
    rules: [
      {
        required: true,
        message: '请选择业务类型',
      },
    ],
  },
  {
    fieldName: 'employeeRelation',
    label: '员工关系',
    component: 'Select',
    componentProps: {
      placeholder: '请选择员工关系',
      options: [
        { label: '劳务派遣', value: '劳务派遣' },
        { label: '人事代理', value: '人事代理' },
        { label: '业务外包', value: '业务外包' },
        { label: '灵活用工', value: '灵活用工' },
      ],
    },
    rules: [
      {
        required: true,
        message: '请选择员工关系',
      },
    ],
  },
  {
    fieldName: 'projectStaffCount',
    label: '项目在职人数',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入项目在职人数',
      min: 0,
      precision: 0,
    },
  },
  {
    fieldName: 'projectManagerId',
    label: '项目经理',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择项目经理',
      api: getUserList,
      labelField: 'nickName',
      valueField: 'userId',
    },
  },
  {
    fieldName: 'settlementSpecialistId',
    label: '结算专员',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择结算专员',
      api: getUserList,
      labelField: 'nickName',
      valueField: 'userId',
    },
  },
  {
    fieldName: 'departmentId',
    label: '所属部门',
    component: 'ApiTreeSelect',
    componentProps: {
      placeholder: '请选择所属部门',
      api: getDeptList,
      fieldNames: {
        label: 'deptName',
        value: 'deptId',
      },
    },
    rules: [
      {
        required: true,
        message: '请选择所属部门',
      },
    ],
  },
  {
    fieldName: 'projectStatus',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目状态',
      options: [
        { label: '进行中', value: '进行中' },
        { label: '已完成', value: '已完成' },
        { label: '已暂停', value: '已暂停' },
        { label: '已取消', value: '已取消' },
      ],
    },
    rules: [
      {
        required: true,
        message: '请选择项目状态',
      },
    ],
  },
  {
    fieldName: 'projectType',
    label: '项目类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目类型',
      options: [
        { label: '长期项目', value: '长期项目' },
        { label: '短期项目', value: '短期项目' },
        { label: '临时项目', value: '临时项目' },
      ],
    },
  },
  {
    fieldName: 'projectBudget',
    label: '项目预算',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入项目预算',
      min: 0,
      precision: 2,
      formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
    },
  },
  {
    fieldName: 'projectStartDate',
    label: '项目开始日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择项目开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    fieldName: 'projectEndDate',
    label: '项目结束日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择项目结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    fieldName: 'remark',
    label: '备注信息',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
    rules: [
      {
        max: 500,
        message: '备注信息长度不能超过500个字符',
      },
    ],
  },
];
