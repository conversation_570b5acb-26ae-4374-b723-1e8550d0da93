import type { VxeGridProps } from '#/adapter/vxe-table';
import type { FormSchemaGetter } from '#/types';

import { deptList } from '#/api/system/dept';
import { userList } from '#/api/system/user';

/**
 * 查询表单配置
 */
export const formSchemaGetter: FormSchemaGetter = () => [
  {
    fieldName: 'projectName',
    label: '项目名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目名称',
    },
  },
  {
    fieldName: 'businessType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择业务类型',
      options: [
        { label: '外包服务', value: '外包服务' },
        { label: '人力派遣', value: '人力派遣' },
        { label: '技术咨询', value: '技术咨询' },
        { label: '项目外包', value: '项目外包' },
      ],
    },
  },
  {
    fieldName: 'employeeRelation',
    label: '员工关系',
    component: 'Select',
    componentProps: {
      placeholder: '请选择员工关系',
      options: [
        { label: '劳务派遣', value: '劳务派遣' },
        { label: '人事代理', value: '人事代理' },
        { label: '业务外包', value: '业务外包' },
        { label: '灵活用工', value: '灵活用工' },
      ],
    },
  },
  {
    fieldName: 'projectStatus',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目状态',
      options: [
        { label: '进行中', value: '进行中' },
        { label: '已完成', value: '已完成' },
        { label: '已暂停', value: '已暂停' },
        { label: '已取消', value: '已取消' },
      ],
    },
  },
  {
    fieldName: 'departmentId',
    label: '所属部门',
    component: 'ApiTreeSelect',
    componentProps: {
      placeholder: '请选择所属部门',
      api: deptList,
      fieldNames: {
        label: 'deptName',
        value: 'deptId',
      },
    },
  },
];

/**
 * 表格列配置
 */
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '项目ID',
    field: 'id',
    width: 80,
  },
  {
    title: '项目名称',
    field: 'projectName',
    width: 200,
    showOverflow: 'tooltip',
  },
  {
    title: '项目在职人数',
    field: 'projectStaffCount',
    width: 120,
    formatter({ cellValue }) {
      return cellValue ? `${cellValue}人` : '0人';
    },
  },
  {
    title: '业务类型',
    field: 'businessType',
    width: 120,
  },
  {
    title: '员工关系',
    field: 'employeeRelation',
    width: 120,
  },
  {
    title: '项目经理',
    field: 'projectManagerName',
    width: 120,
    formatter({ cellValue }) {
      return cellValue || '暂无';
    },
  },
  {
    title: '结算专员',
    field: 'settlementSpecialistName',
    width: 120,
    formatter({ cellValue }) {
      return cellValue || '暂无';
    },
  },
  {
    title: '所属部门',
    field: 'departmentName',
    width: 150,
    formatter({ cellValue }) {
      return cellValue || '暂无';
    },
  },
  {
    title: '项目状态',
    field: 'projectStatus',
    width: 100,
    cellRender: {
      name: 'CellTag',
      props: ({ row }) => {
        const status = row.projectStatus;
        let color = 'default';
        if (status === '进行中') color = 'processing';
        else if (status === '已完成') color = 'success';
        else if (status === '已暂停') color = 'warning';
        else if (status === '已取消') color = 'error';
        return { color, text: status };
      },
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    resizable: false,
    width: 'auto',
  },
];
