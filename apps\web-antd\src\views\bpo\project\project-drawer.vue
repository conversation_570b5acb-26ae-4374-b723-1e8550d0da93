<template>
  <DrawerComponent
    :title="getTitle"
    :loading="loading"
    @confirm="handleSubmit"
  >
    <FormComponent />
  </DrawerComponent>
</template>

<script setup lang="ts">
import { computed, ref, unref } from 'vue';
import { message } from 'ant-design-vue';

import type { ProjectBo, ProjectVo } from '#/api/bpo/project/model';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';

import { addProject, getProject, updateProject } from '#/api/bpo/project';

import { drawerSchemaGetter } from './drawer-data';

defineOptions({
  name: 'ProjectDrawer',
});

const emit = defineEmits<{
  success: [];
}>();

const [DrawerComponent, drawerApi] = useVbenDrawer();

const [FormComponent, formApi] = useVbenForm({
  schema: drawerSchemaGetter(),
});

const loading = ref(false);
const isUpdate = ref(false);
const recordId = ref<number>();

// 抽屉标题
const getTitle = computed(() => {
  return unref(isUpdate) ? '编辑项目' : '新增项目';
});

/**
 * 打开抽屉
 */
drawerApi.onOpenChange(async (isOpen) => {
  if (isOpen) {
    const data = drawerApi.getData();
    console.log('抽屉打开，接收到的数据:', data);
    
    if (data?.id) {
      // 编辑模式
      isUpdate.value = true;
      recordId.value = data.id;
      await loadProjectData(data.id);
    } else {
      // 新增模式
      isUpdate.value = false;
      recordId.value = undefined;
      // 重置表单
      await formApi.resetForm();
    }
  }
});

/**
 * 加载项目数据
 */
async function loadProjectData(id: number) {
  try {
    loading.value = true;
    const result = await getProject(id);
    console.log('加载项目数据:', result);
    
    if (result) {
      // 设置表单值
      await formApi.setValues(result);
    }
  } catch (error) {
    console.error('加载项目数据失败:', error);
    message.error('加载项目数据失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    // 验证表单
    const valid = await formApi.validate();
    if (!valid) {
      return;
    }

    loading.value = true;
    const values = await formApi.getValues();
    console.log('提交表单数据:', values);

    const submitData: ProjectBo = {
      ...values,
      id: unref(recordId),
    };

    if (unref(isUpdate)) {
      // 更新项目
      await updateProject(submitData);
      message.success('更新项目成功');
    } else {
      // 新增项目
      await addProject(submitData);
      message.success('新增项目成功');
    }

    // 关闭抽屉
    drawerApi.close();
    
    // 触发成功事件
    emit('success');
  } catch (error) {
    console.error('提交项目数据失败:', error);
    message.error('操作失败，请重试');
  } finally {
    loading.value = false;
  }
}
</script>
