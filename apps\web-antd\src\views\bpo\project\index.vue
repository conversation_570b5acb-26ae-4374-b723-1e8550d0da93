<template>
  <Page
    description="项目信息管理，支持项目的新增、编辑、删除、导出等操作"
    title="项目管理"
  >
    <BasicTable>
      <template #toolbar>
        <Button
          type="primary"
          @click="handleAdd"
        >
          <Icon icon="lucide:plus" class="mr-2" />
          新增项目
        </Button>
        <Button
          type="default"
          @click="handleImport"
        >
          <Icon icon="lucide:upload" class="mr-2" />
          导入项目
        </Button>
        <Button
          type="default"
          @click="handleExport"
        >
          <Icon icon="lucide:download" class="mr-2" />
          导出项目
        </Button>
        <Button
          v-if="tableApi.getCheckboxRecords().length > 0"
          color="error"
          @click="handleBatchDelete"
        >
          <Icon icon="lucide:trash-2" class="mr-2" />
          批量删除
        </Button>
      </template>

      <template #action="{ record }">
        <div class="flex gap-2">
          <Button
            size="small"
            type="link"
            @click="handleEdit(record)"
          >
            <Icon icon="lucide:edit" />
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个项目吗？"
            @confirm="handleDelete(record)"
          >
            <Button
              size="small"
              type="link"
              danger
            >
              <Icon icon="lucide:trash-2" />
              删除
            </Button>
          </Popconfirm>
        </div>
      </template>
    </BasicTable>

    <!-- 项目编辑抽屉 -->
    <ProjectDrawerComponent @success="handleSuccess" />

    <!-- 项目导入模态框 -->
    <ProjectImportModalComponent @success="handleSuccess" />
  </Page>
</template>

<script setup lang="ts">
import { Button, message, Popconfirm } from 'ant-design-vue';

import type { ProjectVo } from '#/api/bpo/project/model';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useVbenDrawer } from '#/components/drawer';
import { Icon } from '#/components/icon';
import { useVbenModal } from '#/components/modal';
import { Page } from '#/components/page';

import { delProject, exportProject, getProjectList } from '#/api/bpo/project';

import { columns, formSchemaGetter } from './data';
import ProjectDrawer from './project-drawer.vue';
import ProjectImportModal from './project-import-modal.vue';

defineOptions({
  name: 'BpoProjectIndex',
});

// 表单配置
const formOptions = {
  schema: formSchemaGetter(),
};

// 表格配置
const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
    trigger: 'cell',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const result = await getProjectList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });

        console.log('项目列表API返回数据:', result);
        return result;
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'bpo-project-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [ProjectDrawerComponent, drawerApi] = useVbenDrawer({
  connectedComponent: ProjectDrawer,
});

const [ProjectImportModalComponent, importModalApi] = useVbenModal({
  connectedComponent: ProjectImportModal,
});

/**
 * 新增项目
 */
function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

/**
 * 编辑项目
 */
async function handleEdit(record: ProjectVo) {
  drawerApi.setData({ id: record.id });
  drawerApi.open();
}

/**
 * 删除项目
 */
async function handleDelete(record: ProjectVo) {
  try {
    await delProject([record.id!]);
    message.success('删除成功');
    await tableApi.reload();
  } catch (error) {
    console.error('删除项目失败:', error);
  }
}

/**
 * 批量删除项目
 */
async function handleBatchDelete() {
  const rows = tableApi.getCheckboxRecords();
  if (rows.length === 0) {
    message.warning('请选择要删除的项目');
    return;
  }

  try {
    const ids = rows.map((row) => row.id);
    await delProject(ids);
    message.success('批量删除成功');
    await tableApi.reload();
  } catch (error) {
    console.error('批量删除项目失败:', error);
  }
}

/**
 * 导出项目
 */
async function handleExport() {
  try {
    const formValues = tableApi.getFormValues();
    await exportProject(formValues);
    message.success('导出成功');
  } catch (error) {
    console.error('导出项目失败:', error);
  }
}

/**
 * 导入项目
 */
function handleImport() {
  importModalApi.open();
}

/**
 * 操作成功回调
 */
async function handleSuccess() {
  await tableApi.reload();
}
</script>
