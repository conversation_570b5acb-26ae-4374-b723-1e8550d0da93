<template>
  <Page
    description="项目信息管理，支持项目的新增、编辑、删除、导出等操作"
    title="项目管理"
  >
    <BasicTable>
      <template #toolbar>
        <Space>
          <GhostButton
            type="primary"
            @click="handleAdd"
          >
            新增项目
          </GhostButton>
          <GhostButton
            @click="handleImport"
          >
            导入项目
          </GhostButton>
          <GhostButton
            @click="handleExport"
          >
            导出项目
          </GhostButton>
          <GhostButton
            v-if="vxeCheckboxChecked(gridApi)"
            danger
            @click="handleBatchDelete"
          >
            批量删除
          </GhostButton>
        </Space>
      </template>

      <template #action="{ record }">
        <Space>
          <GhostButton
            size="small"
            type="link"
            @click="handleEdit(record)"
          >
            编辑
          </GhostButton>
          <Popconfirm
            title="确定要删除这个项目吗？"
            @confirm="handleDelete(record)"
          >
            <GhostButton
              size="small"
              type="link"
              danger
            >
              删除
            </GhostButton>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>

    <!-- 项目编辑抽屉 -->
    <ProjectDrawerComponent @reload="handleSuccess" />

    <!-- 项目导入模态框 -->
    <ProjectImportModalComponent @reload="handleSuccess" />
  </Page>
</template>

<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ProjectVo } from '#/api/bpo/project/model';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import {
  Button as GhostButton,
  message,
  Popconfirm,
  Space,
} from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { delProject, exportProject, getProjectList } from '#/api/bpo/project';

import { columns, formSchemaGetter } from './data';
import ProjectDrawer from './project-drawer.vue';
import ProjectImportModal from './project-import-modal.vue';

defineOptions({ name: 'BpoProject' });

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      allowClear: true,
    },
  },
  schema: formSchemaGetter(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
    trigger: 'cell',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const result = await getProjectList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });

        console.log('项目列表API返回数据:', result);
        return result;
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'bpo-project-index',
};

const [BasicTable, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [ProjectDrawerComponent, drawerApi] = useVbenDrawer({
  connectedComponent: ProjectDrawer,
});

const [ProjectImportModalComponent, importModalApi] = useVbenModal({
  connectedComponent: ProjectImportModal,
});

/**
 * 新增项目
 */
function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

/**
 * 编辑项目
 */
async function handleEdit(record: ProjectVo) {
  drawerApi.setData({ id: record.id });
  drawerApi.open();
}

/**
 * 删除项目
 */
async function handleDelete(record: ProjectVo) {
  try {
    await delProject([record.id!]);
    message.success('删除成功');
    await gridApi.reload();
  } catch (error) {
    console.error('删除项目失败:', error);
  }
}

/**
 * 批量删除项目
 */
async function handleBatchDelete() {
  const rows = gridApi.getCheckboxRecords();
  if (rows.length === 0) {
    message.warning('请选择要删除的项目');
    return;
  }

  try {
    const ids = rows.map((row) => row.id);
    await delProject(ids);
    message.success('批量删除成功');
    await gridApi.reload();
  } catch (error) {
    console.error('批量删除项目失败:', error);
  }
}

/**
 * 导出项目
 */
async function handleExport() {
  try {
    const formValues = gridApi.getFormValues();
    await exportProject(formValues);
    message.success('导出成功');
  } catch (error) {
    console.error('导出项目失败:', error);
  }
}

/**
 * 导入项目
 */
function handleImport() {
  importModalApi.open();
}

/**
 * 操作成功回调
 */
async function handleSuccess() {
  await gridApi.reload();
}
</script>
