<template>
  <Page
    description="项目信息管理，支持项目的新增、编辑、删除、导出等操作"
    title="项目管理"
  >
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button
          type="primary"
          @click="handleAdd"
        >
          <Icon icon="lucide:plus" class="mr-2" />
          新增项目
        </Button>
        <Button
          type="default"
          @click="handleImport"
        >
          <Icon icon="lucide:upload" class="mr-2" />
          导入项目
        </Button>
        <Button
          type="default"
          @click="handleExport"
        >
          <Icon icon="lucide:download" class="mr-2" />
          导出项目
        </Button>
      </template>

      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'lucide:edit',
              label: '编辑',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'lucide:trash-2',
              label: '删除',
              color: 'error',
              popConfirm: {
                title: '是否确认删除？',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>

    <!-- 项目编辑抽屉 -->
    <ProjectDrawer @register="registerDrawer" @success="handleSuccess" />

    <!-- 项目导入模态框 -->
    <ProjectImportModal @register="registerImportModal" @success="handleSuccess" />
  </Page>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Button, message } from 'ant-design-vue';

import type { ProjectVo } from '#/api/bpo/project/model';

import { Icon } from '#/components/icon';
import { Page } from '#/components/page';
import { BasicTable, TableAction, useTable } from '#/components/table';
import { useModal } from '#/components/modal';

import { delProject, exportProject, getProjectList } from '#/api/bpo/project';

import { columns, formSchemaGetter } from './data';
import ProjectDrawer from './project-drawer.vue';
import ProjectImportModal from './project-import-modal.vue';

defineOptions({
  name: 'BpoProjectIndex',
});

// 表格配置
const [registerTable, { getForm, reload, getSelectRows }] = useTable({
  title: '项目列表',
  api: getProjectList,
  columns,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemaGetter(),
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
  rowSelection: {
    type: 'checkbox',
  },
});

// 抽屉配置
const [registerDrawer, { openDrawer }] = useModal();

// 导入模态框配置
const [registerImportModal, { openModal: openImportModal }] = useModal();

/**
 * 新增项目
 */
function handleAdd() {
  openDrawer(true, {
    isUpdate: false,
  });
}

/**
 * 编辑项目
 */
function handleEdit(record: ProjectVo) {
  openDrawer(true, {
    record,
    isUpdate: true,
  });
}

/**
 * 删除项目
 */
async function handleDelete(record: ProjectVo) {
  try {
    await delProject([record.id!]);
    message.success('删除成功');
    await reload();
  } catch (error) {
    console.error('删除项目失败:', error);
  }
}

/**
 * 批量删除项目
 */
async function handleBatchDelete() {
  const rows = getSelectRows();
  if (rows.length === 0) {
    message.warning('请选择要删除的项目');
    return;
  }

  try {
    const ids = rows.map((row) => row.id);
    await delProject(ids);
    message.success('批量删除成功');
    await reload();
  } catch (error) {
    console.error('批量删除项目失败:', error);
  }
}

/**
 * 导出项目
 */
async function handleExport() {
  try {
    const formValues = getForm().getFieldsValue();
    await exportProject(formValues);
    message.success('导出成功');
  } catch (error) {
    console.error('导出项目失败:', error);
  }
}

/**
 * 导入项目
 */
function handleImport() {
  openImportModal(true);
}

/**
 * 操作成功回调
 */
async function handleSuccess() {
  await reload();
}
</script>
